export function initMasonry() {
  console.log("🚀 initMasonry 函数开始执行");

  var loadingPlaceholder = document.querySelector(".loading-placeholder");
  var masonryContainer = document.querySelector("#masonry-container");

  console.log("DOM 元素查找结果:");
  console.log("  - loadingPlaceholder:", loadingPlaceholder);
  console.log("  - masonryContainer:", masonryContainer);

  if (!loadingPlaceholder || !masonryContainer) {
    console.log("❌ 必要的 DOM 元素未找到，退出 initMasonry");
    return;
  }

  console.log("✅ DOM 元素找到，开始初始化 Masonry");

  loadingPlaceholder.style.display = "block";
  masonryContainer.style.display = "none";

  //init Masonry without wait since <img> already have its size
  console.log("🔧 创建 MiniMasonry 实例");
  console.log("  - MiniMasonry 是否存在:", typeof MiniMasonry !== 'undefined');

  const masonry = new MiniMasonry({
    container: masonryContainer,
    baseWidth: window.innerWidth >= 768 ? 255 : 150,
    gutterX: 10,
    gutterY: 10,
    surroundingGutter: false,
  });

  console.log("📐 执行 masonry.layout()");
  masonry.layout();

  loadingPlaceholder.style.display = "none";
  loadingPlaceholder.style.opacity = 0;
  masonryContainer.style.display = "block";
  masonryContainer.style.opacity = 1;

  console.log("✅ Masonry 初始化完成");
}

// 添加调试信息
console.log("=== Masonry.js 脚本开始执行 ===");
console.log("window.data:", window.data);
console.log("typeof data:", typeof data);
console.log("data:", data);

if (typeof data !== 'undefined' && data.masonry) {
  console.log("✅ Masonry data found - 条件满足");
  try {
    console.log("swup 对象:", swup);
    swup.hooks.on("page:view", ()=>{
      console.log("🔄 Swup page:view 事件触发");
      initMasonry();
      if (window.lazySizes) {
        console.log("✅ lazySizes initialized");
        window.lazySizes.init();
      }
      console.log("✅ Masonry initialized via swup");
    });
  } catch (e) {
    console.log("❌ Masonry swup init failed: " + e);
  }

  document.addEventListener("DOMContentLoaded", () => {
    console.log("🔄 DOMContentLoaded 事件触发");
    initMasonry();
  });
} else {
  console.log("❌ Masonry 条件不满足:");
  console.log("  - data 是否存在:", typeof data !== 'undefined');
  console.log("  - data.masonry 值:", typeof data !== 'undefined' ? data.masonry : 'data未定义');
}


