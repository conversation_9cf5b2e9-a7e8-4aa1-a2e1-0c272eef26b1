export function initMasonry() {
  console.log("🚀 initMasonry 函数开始执行");

  var loadingPlaceholder = document.querySelector(".loading-placeholder");
  var masonryContainer = document.querySelector("#masonry-container");

  console.log("DOM 元素查找结果:");
  console.log("  - loadingPlaceholder:", loadingPlaceholder);
  console.log("  - masonryContainer:", masonryContainer);

  if (!loadingPlaceholder || !masonryContainer) {
    console.log("❌ 必要的 DOM 元素未找到，退出 initMasonry");
    return;
  }

  console.log("✅ DOM 元素找到，开始初始化 Masonry");

  loadingPlaceholder.style.display = "block";
  masonryContainer.style.display = "none";

  // 获取所有图片元素
  var images = document.querySelectorAll("#masonry-container .masonry-item img");
  var loadedCount = 0;

  console.log("📷 找到图片数量:", images.length);

  // 图片加载完成的回调函数
  function onImageLoad() {
    loadedCount++;
    console.log(`📷 图片加载进度: ${loadedCount}/${images.length}`);
    if (loadedCount === images.length) {
      initializeMasonryLayout();
    }
  }

  // 检查每张图片的加载状态
  for (var i = 0; i < images.length; i++) {
    var img = images[i];
    if (img.complete) {
      console.log(`📷 图片 ${i+1} 已缓存，直接计数`);
      onImageLoad();
    } else {
      console.log(`📷 图片 ${i+1} 等待加载`);
      img.addEventListener("load", onImageLoad);
    }
  }

  // 如果没有图片，直接初始化
  if (images.length === 0) {
    console.log("📷 没有图片，直接初始化布局");
    initializeMasonryLayout();
  }

  // 布局初始化函数
  function initializeMasonryLayout() {
    console.log("🎯 开始初始化 Masonry 布局");

    loadingPlaceholder.style.opacity = 0;

    setTimeout(() => {
      loadingPlaceholder.style.display = "none";
      masonryContainer.style.display = "block";

      var baseWidth = window.innerWidth >= 768 ? 255 : 150;
      console.log("📐 基础宽度:", baseWidth);
      console.log("🔧 创建 MiniMasonry 实例");
      console.log("  - MiniMasonry 是否存在:", typeof MiniMasonry !== 'undefined');

      var masonry = new MiniMasonry({
        baseWidth: baseWidth,
        container: masonryContainer,
        gutterX: 10,
        gutterY: 10,
        surroundingGutter: false,
      });

      console.log("📐 执行 masonry.layout()");
      masonry.layout();

      masonryContainer.style.opacity = 1;
      console.log("✅ Masonry 布局初始化完成");
    }, 100);
  }
}

// 添加调试信息
console.log("=== Masonry.js 脚本开始执行 ===");
console.log("window.data:", window.data);
console.log("typeof data:", typeof data);
console.log("data:", data);

if (typeof data !== 'undefined' && data.masonry) {
  console.log("✅ Masonry data found - 条件满足");
  try {
    console.log("swup 对象:", swup);
    swup.hooks.on("page:view", ()=>{
      console.log("🔄 Swup page:view 事件触发");
      initMasonry();
      if (window.lazySizes) {
        console.log("✅ lazySizes initialized");
        window.lazySizes.init();
      }
      console.log("✅ Masonry initialized via swup");
    });
  } catch (e) {
    console.log("❌ Masonry swup init failed: " + e);
  }

  document.addEventListener("DOMContentLoaded", () => {
    console.log("🔄 DOMContentLoaded 事件触发");
    initMasonry();
  });
} else {
  console.log("❌ Masonry 条件不满足:");
  console.log("  - data 是否存在:", typeof data !== 'undefined');
  console.log("  - data.masonry 值:", typeof data !== 'undefined' ? data.masonry : 'data未定义');
}
//# sourceMappingURL=masonry.js.map